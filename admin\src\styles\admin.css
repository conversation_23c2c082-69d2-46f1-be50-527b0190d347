/* Reset and base styles */

/* --- Admin Sidebar Styles --- */
.admin-sidebar {
  width: 250px;
  min-height: 100vh;
  background: var(--bg-primary);
  box-shadow: 2px 0 12px rgba(30, 41, 59, 0.06);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  transition: width 0.3s;
}

.admin-sidebar__header {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  letter-spacing: 0.5px;
}

.admin-sidebar__close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.2s;
}
.admin-sidebar__close:hover {
  color: var(--primary-dark);
}

.admin-sidebar__menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 1rem;
}

.admin-sidebar__menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.85rem 1rem;
  color: var(--text-secondary);
  font-size: 1.08rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-decoration: none;
  transition: background 0.18s, color 0.18s;
  cursor: pointer;
}

.admin-sidebar__menu-item svg {
  font-size: 1.3rem;
}

.admin-sidebar__menu-item:hover,
.admin-sidebar__menu-item:focus {
  background: var(--bg-tertiary);
  color: var(--primary-color);
}

.admin-sidebar__menu-item.active {
  background: var(--primary-color);
  color: var(--text-white);
  font-weight: 700;
}

.admin-sidebar__footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-light);
  color: var(--text-tertiary);
  font-size: 0.97rem;
}

@media (max-width: 900px) {
  .admin-sidebar {
    width: 200px;
  }
  .admin-sidebar__header {
    font-size: 1.08rem;
    padding: 1.1rem 1rem 0.8rem 1rem;
  }
  .admin-sidebar__footer {
    padding: 0.8rem 1rem;
  }
}

@media (max-width: 600px) {
  .admin-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    width: 85vw;
    min-width: 0;
    max-width: 350px;
    box-shadow: 2px 0 16px rgba(30, 41, 59, 0.13);
    min-height: 100vh;
    background: var(--bg-primary);
    transition: left 0.25s, width 0.25s;
  }
  .admin-sidebar__menu {
    padding: 0 0.5rem;
  }
}
/* --- End Admin Sidebar Styles --- */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  
  /* Color variables */
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  /* Background colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #1e293b;
  --bg-darker: #0f172a;
  
  /* Text colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-white: #ffffff;
  
  /* Border colors */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-dark: #94a3b8;
  
  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
}

body {
  margin: 0;
  font-family: var(--font-family);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  min-height: 100vh;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: var(--spacing-sm);
}

.gap-4 {
  gap: var(--spacing-md);
}

.gap-6 {
  gap: var(--spacing-lg);
}

.p-2 {
  padding: var(--spacing-sm);
}

.p-4 {
  padding: var(--spacing-md);
}

.p-6 {
  padding: var(--spacing-lg);
}

.px-4 {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

.py-2 {
  padding-top: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
}

.py-4 {
  padding-top: var(--spacing-md);
  padding-bottom: var(--spacing-md);
}

.m-4 {
  margin: var(--spacing-md);
}

.mb-4 {
  margin-bottom: var(--spacing-md);
}

.mt-4 {
  margin-top: var(--spacing-md);
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.bg-white {
  background-color: var(--bg-primary);
}

.bg-gray-50 {
  background-color: var(--bg-secondary);
}

.bg-gray-100 {
  background-color: var(--bg-tertiary);
}

.text-gray-600 {
  color: var(--text-secondary);
}

.text-gray-500 {
  color: var(--text-tertiary);
}

.border {
  border: 1px solid var(--border-light);
}

.border-gray-200 {
  border-color: var(--border-light);
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.bottom-0 {
  bottom: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-50 {
  z-index: 50;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  gap: var(--spacing-xs);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-error {
  background-color: var(--error-color);
  color: var(--text-white);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
}

/* Form styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color 0.2s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  cursor: pointer;
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
}

/* Card styles */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-secondary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.table th {
  background-color: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* Loading spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Additional utility classes */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-1 > * + * {
  margin-left: 0.25rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-700 {
  color: #374151;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #059669;
}

.text-yellow-600 {
  color: #d97706;
}

.text-red-600 {
  color: #dc2626;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-green-800 {
  color: #166534;
}

.text-red-800 {
  color: #991b1b;
}

.text-yellow-900 {
  color: #78350f;
}

.text-gray-400 {
  color: #9ca3af;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.bg-yellow-400 {
  background-color: #facc15;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-opacity-20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-opacity-30 {
  background-color: rgba(255, 255, 255, 0.3);
}

.border-2 {
  border-width: 2px;
}

.border-yellow-400 {
  border-color: #facc15;
}

.border-white {
  border-color: #ffffff;
}

.border-opacity-50 {
  border-color: rgba(255, 255, 255, 0.5);
}

.border-opacity-20 {
  border-color: rgba(255, 255, 255, 0.2);
}

.border-t {
  border-top-width: 1px;
}



.rounded-full {
  border-radius: 9999px;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.p-5 {
  padding: 1.25rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.w-4 {
  width: 1rem;
}

.w-12 {
  width: 3rem;
}

.h-4 {
  height: 1rem;
}

.h-12 {
  height: 3rem;
}

.min-w-0 {
  min-width: 0px;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.object-cover {
  object-fit: cover;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

.hover\:bg-opacity-30:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.opacity-90 {
  opacity: 0.9;
}

.opacity-75 {
  opacity: 0.75;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.text-center {
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-md);
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
  }
}
