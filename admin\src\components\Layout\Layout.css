/* Layout - Tailwind to CSS migration */

.layout-root {
  min-height: 100vh;
  display: flex;
  background: #f9fafb;
}

.layout-sidebar {
  width: 16rem;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 4px 24px rgba(30, 64, 175, 0.10);
}

.layout-sidebar-header {
  display: flex;
  align-items: center;
  height: 4rem;
  padding: 0 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(to right, #2563eb, #1d4ed8);
}

.layout-logo {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255,255,255,0.2);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.layout-logo-icon {
  color: #fff;
  font-weight: bold;
  font-size: 1.125rem;
}

.layout-title {
  margin-left: 0.75rem;
}

.layout-title-main {
  color: #fff;
  font-weight: bold;
  font-size: 1.25rem;
}

.layout-title-sub {
  color: #dbeafe;
  font-size: 0.75rem;
}

.layout-nav {
  margin-top: 1.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  flex: 1;
  overflow-y: auto;
}

.layout-nav-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.layout-nav-btn {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.75rem;
  transition: all 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  color: #4b5563;
}
.layout-nav-btn.active {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  color: #fff;
  box-shadow: 0 4px 24px rgba(59, 130, 246, 0.25);
}
.layout-nav-btn:not(.active):hover {
  background: #f1f5f9;
  color: #111827;
  box-shadow: 0 1px 4px rgba(30, 64, 175, 0.06);
}

.layout-nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
}
